const { pool } = require('./server/config/db');

async function checkSpreadIds() {
  try {
    console.log('检查数据库中的spread_id值...');
    
    // 查看所有不同的spread_id值
    const [spreadIds] = await pool.query(`
      SELECT DISTINCT spread_id, COUNT(*) as count
      FROM sessions 
      WHERE timestamp >= '2025-07-29 00:00:00'
        AND status = 'completed'
      GROUP BY spread_id
      ORDER BY count DESC
    `);
    
    console.log('所有spread_id值及其数量:');
    spreadIds.forEach(item => {
      console.log(`- "${item.spread_id}": ${item.count} 次`);
    });
    
    // 检查包含 'daily' 或 'fortune' 的spread_id
    const [dailyFortuneIds] = await pool.query(`
      SELECT DISTINCT spread_id, COUNT(*) as count
      FROM sessions 
      WHERE timestamp >= '2025-07-29 00:00:00'
        AND status = 'completed'
        AND (spread_id LIKE '%daily%' OR spread_id LIKE '%fortune%')
      GROUP BY spread_id
      ORDER BY count DESC
    `);
    
    console.log('\n包含"daily"或"fortune"的spread_id:');
    dailyFortuneIds.forEach(item => {
      console.log(`- "${item.spread_id}": ${item.count} 次`);
    });
    
  } catch (error) {
    console.error('检查spread_id失败:', error);
  } finally {
    process.exit(0);
  }
}

checkSpreadIds();
