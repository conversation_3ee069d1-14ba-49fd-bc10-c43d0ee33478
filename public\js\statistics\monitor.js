// 监控相关功能
let monitorData = {};

// 初始化监控功能
function initMonitor() {
  console.log('初始化监控功能');
  
  // 绑定刷新按钮事件
  const refreshBtn = document.getElementById('refresh-monitor');
  if (refreshBtn) {
    refreshBtn.addEventListener('click', loadMonitorOverview);
  }
  
  // 绑定监控标签页切换事件
  const monitorTabs = document.querySelectorAll('.monitor-tab');
  monitorTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const targetType = this.getAttribute('data-type');
      switchMonitorTab(targetType);
    });
  });
  
  // 绑定详情按钮事件
  const detailBtns = document.querySelectorAll('.btn-detail');
  detailBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      const action = this.getAttribute('data-action');
      handleDetailAction(action);
    });
  });
  
  // 初始加载概览数据
  loadMonitorOverview();
}

// 加载监控概览数据
async function loadMonitorOverview() {
  try {
    console.log('加载监控概览数据');
    const refreshBtn = document.getElementById('refresh-monitor');
    if (refreshBtn) {
      refreshBtn.disabled = true;
      refreshBtn.textContent = '加载中...';
    }
    
    const response = await axios.get('/api/monitor-overview');
    
    if (response.data.success) {
      const data = response.data.data;
      monitorData.overview = data;
      
      // 更新概览数据显示
      updateOverviewDisplay(data);
      
      console.log('监控概览数据加载成功');
    } else {
      throw new Error(response.data.message || '获取监控概览数据失败');
    }
  } catch (error) {
    console.error('加载监控概览数据失败:', error);
    showError('加载监控概览数据失败: ' + error.message);
  } finally {
    const refreshBtn = document.getElementById('refresh-monitor');
    if (refreshBtn) {
      refreshBtn.disabled = false;
      refreshBtn.textContent = '刷新监控数据';
    }
  }
}

// 更新概览数据显示
function updateOverviewDisplay(data) {
  // 更新各项统计数据
  const elements = {
    'duplicate-ips': data.duplicateIPs,
    'duplicate-fingerprints': data.duplicateFingerprints,
    'anonymous-empty-spread': data.anonymousEmptySpreadId,
    'non-vip-multiple': data.nonVipMultipleCompleted,
    'sessions-empty-spread': data.sessionsEmptySpreadId
  };
  
  Object.entries(elements).forEach(([id, value]) => {
    const element = document.getElementById(id);
    if (element) {
      element.textContent = value || 0;
      
      // 根据数值添加警告样式
      const card = element.closest('.stat-card');
      if (card) {
        card.classList.remove('warning', 'danger');
        if (value > 0) {
          if (value > 10) {
            card.classList.add('danger');
          } else {
            card.classList.add('warning');
          }
        }
      }
    }
  });
  
  // 更新最后更新时间
  const lastUpdatedElement = document.getElementById('last-updated');
  if (lastUpdatedElement && data.lastUpdated) {
    const updateTime = new Date(data.lastUpdated).toLocaleString('zh-CN');
    lastUpdatedElement.textContent = `最后更新: ${updateTime}`;
  }
}

// 切换监控标签页
function switchMonitorTab(targetType) {
  // 更新标签页状态
  const tabs = document.querySelectorAll('.monitor-tab');
  tabs.forEach(tab => {
    tab.classList.remove('active');
    if (tab.getAttribute('data-type') === targetType) {
      tab.classList.add('active');
    }
  });
  
  // 切换内容显示
  const contents = document.querySelectorAll('.monitor-content');
  contents.forEach(content => {
    content.style.display = 'none';
  });
  
  const targetContent = document.getElementById(`monitor-${targetType}`);
  if (targetContent) {
    targetContent.style.display = 'block';
  }
}

// 处理详情按钮点击
async function handleDetailAction(action) {
  try {
    console.log('执行详情操作:', action);
    
    const btn = document.querySelector(`[data-action="${action}"]`);
    if (btn) {
      btn.disabled = true;
      btn.textContent = '加载中...';
    }
    
    switch (action) {
      case 'load-duplicate-ips':
        await loadDuplicateIPs();
        break;
      case 'load-duplicate-fingerprints':
        await loadDuplicateFingerprints();
        break;
      case 'load-anonymous-empty-spread':
        await loadAnonymousEmptySpread();
        break;
      case 'load-sessions-empty-spread':
        await loadSessionsEmptySpread();
        break;
      case 'load-non-vip-multiple':
        await loadNonVipMultiple();
        break;
      default:
        console.warn('未知的详情操作:', action);
    }
  } catch (error) {
    console.error('执行详情操作失败:', error);
    showError('加载详情数据失败: ' + error.message);
  } finally {
    const btn = document.querySelector(`[data-action="${action}"]`);
    if (btn) {
      btn.disabled = false;
      btn.textContent = '查看详情';
    }
  }
}

// 加载重复IP数据
async function loadDuplicateIPs() {
  const response = await axios.get('/api/monitor/anonymous-duplicates');
  
  if (response.data.success) {
    const data = response.data.data;
    const tableBody = document.querySelector('#duplicate-ips-table tbody');
    
    if (tableBody) {
      if (data.duplicateIPs.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="5" class="no-data">暂无重复IP数据</td></tr>';
      } else {
        tableBody.innerHTML = data.duplicateIPs.map(item => `
          <tr>
            <td>${item.ip_address}</td>
            <td>${item.count}</td>
            <td>${new Date(item.first_occurrence).toLocaleString('zh-CN')}</td>
            <td>${new Date(item.last_occurrence).toLocaleString('zh-CN')}</td>
            <td class="record-ids">${item.record_ids}</td>
          </tr>
        `).join('');
      }
    }
  }
}

// 加载重复指纹数据
async function loadDuplicateFingerprints() {
  const response = await axios.get('/api/monitor/anonymous-duplicates');
  
  if (response.data.success) {
    const data = response.data.data;
    const tableBody = document.querySelector('#duplicate-fingerprints-table tbody');
    
    if (tableBody) {
      if (data.duplicateFingerprints.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="5" class="no-data">暂无重复指纹数据</td></tr>';
      } else {
        tableBody.innerHTML = data.duplicateFingerprints.map(item => `
          <tr>
            <td class="fingerprint-preview">${item.fingerprint_preview}...</td>
            <td>${item.count}</td>
            <td>${new Date(item.first_occurrence).toLocaleString('zh-CN')}</td>
            <td>${new Date(item.last_occurrence).toLocaleString('zh-CN')}</td>
            <td class="record-ids">${item.record_ids}</td>
          </tr>
        `).join('');
      }
    }
  }
}

// 加载匿名记录空牌阵数据
async function loadAnonymousEmptySpread() {
  const response = await axios.get('/api/monitor/anonymous-empty-spread');
  
  if (response.data.success) {
    const data = response.data.data;
    const tableBody = document.querySelector('#anonymous-empty-spread-table tbody');
    
    if (tableBody) {
      if (data.emptySpreadIdRecords.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="5" class="no-data">暂无空牌阵数据</td></tr>';
      } else {
        tableBody.innerHTML = data.emptySpreadIdRecords.map(item => `
          <tr>
            <td>${item.id}</td>
            <td>${item.session_id}</td>
            <td>${item.ip_address || '无'}</td>
            <td class="question-preview">${item.question_preview}...</td>
            <td>${new Date(item.created_at).toLocaleString('zh-CN')}</td>
          </tr>
        `).join('');
      }
    }
  }
}

// 加载会话空牌阵数据
async function loadSessionsEmptySpread() {
  const response = await axios.get('/api/monitor/sessions-empty-spread');
  
  if (response.data.success) {
    const data = response.data.data;
    const tableBody = document.querySelector('#sessions-empty-spread-table tbody');
    
    if (tableBody) {
      if (data.emptySpreadIdSessions.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="no-data">暂无空牌阵会话数据</td></tr>';
      } else {
        tableBody.innerHTML = data.emptySpreadIdSessions.map(item => `
          <tr>
            <td>${item.id}</td>
            <td>${item.username}</td>
            <td>${item.email}</td>
            <td>${item.vip_status}</td>
            <td class="question-preview">${item.question_preview}...</td>
            <td>${new Date(item.timestamp).toLocaleString('zh-CN')}</td>
          </tr>
        `).join('');
      }
    }
  }
}

// 加载非VIP多次占卜数据
async function loadNonVipMultiple() {
  const response = await axios.get('/api/monitor/non-vip-multiple');
  
  if (response.data.success) {
    const data = response.data.data;
    const tableBody = document.querySelector('#non-vip-multiple-table tbody');
    
    if (tableBody) {
      if (data.nonVipMultipleCompleted.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" class="no-data">暂无非VIP滥用数据</td></tr>';
      } else {
        tableBody.innerHTML = data.nonVipMultipleCompleted.map(item => `
          <tr>
            <td>${item.user_id}</td>
            <td>${item.username}</td>
            <td>${item.email}</td>
            <td>${item.vip_status}</td>
            <td class="completed-count">${item.completed_count}</td>
            <td>${new Date(item.first_session).toLocaleString('zh-CN')}</td>
            <td>${new Date(item.last_session).toLocaleString('zh-CN')}</td>
          </tr>
        `).join('');
      }
    }
  }
}

// 导出函数供main.js使用
window.monitorFunctions = {
  initMonitor,
  loadMonitorOverview
};
