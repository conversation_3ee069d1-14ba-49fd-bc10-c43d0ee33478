// 初始化用户图表
function initUserCharts() {
  try {
    // 确保Canvas元素存在并可用
    function getCanvasContext(id) {
      const canvas = document.getElementById(id);
      if (!canvas) {
        throw new Error(`找不到Canvas元素: ${id}`);
      }
      return canvas.getContext('2d');
    }

    const ctxNew = getCanvasContext('userNewChart');
    const ctxCountry = getCanvasContext('userCountryChart');

    // 销毁之前的图表实例
    if (StatisticsState.charts.userNewChart) StatisticsState.charts.userNewChart.destroy();
    if (StatisticsState.charts.userCountryChart) StatisticsState.charts.userCountryChart.destroy();

    // 创建新增用户图表
    StatisticsState.charts.userNewChart = new Chart(ctxNew, {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: '新增用户',
            data: [],
            borderColor: '#3498db',
            backgroundColor: 'rgba(52, 152, 219, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '新增普通用户',
            data: [],
            borderColor: '#2ecc71',
            backgroundColor: 'rgba(46, 204, 113, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '新增VIP用户',
            data: [],
            borderColor: '#f39c12',
            backgroundColor: 'rgba(243, 156, 18, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '羊毛用户',
            data: [],
            borderColor: '#e74c3c',
            backgroundColor: 'rgba(231, 76, 60, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '体验后注册用户',
            data: [],
            borderColor: '#9b59b6',
            backgroundColor: 'rgba(155, 89, 182, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0
            }
          }
        }
      }
    });
    
    // 创建用户国家分布图表
    StatisticsState.charts.userCountryChart = new Chart(ctxCountry, {
      type: 'bar',
      data: {
        labels: [],
        datasets: [
          {
            label: '总用户数',
            data: [],
            backgroundColor: 'rgba(52, 152, 219, 0.7)',
            borderColor: '#3498db',
            borderWidth: 1
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        indexAxis: 'y',
        plugins: {
          title: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false
          },
          legend: {
            position: 'top'
          }
        },
        scales: {
          x: {
            beginAtZero: true,
            ticks: {
              precision: 0
            }
          },
          y: {
            grid: {
              display: false
            }
          }
        }
      }
    });
    
    StatisticsState.flags.userChartsInitialized = true;
  } catch (error) {
    console.error('初始化用户图表出错:', error);
    showError('初始化用户图表失败: ' + error.message);
  }
}

// 更新用户图表数据
function updateUserCharts() {
  if (!StatisticsState.data.userData || !StatisticsState.flags.userChartsInitialized) {
    console.warn('无法更新用户图表：数据不完整或图表未初始化');
    return;
  }
  
  try {
    const { dateRange, totalUsersData, normalUsersData, vipUsersData, newUsersData, newNormalUsersData, newVipUsersData, zeroReadingUsersData, experienceToRegisterUsersData, countryStats } = StatisticsState.data.userData;
    
    // 检查数据完整性
    if (!dateRange || !dateRange.length) {
      console.warn('日期范围数据不完整');
      return;
    }
    
    // 检查必要的数据对象是否存在
    const requiredData = [totalUsersData, normalUsersData, vipUsersData, newUsersData, newNormalUsersData, newVipUsersData, zeroReadingUsersData, experienceToRegisterUsersData];
    if (requiredData.some(data => !data)) {
      console.warn('用户数据不完整');
      return;
    }
    

    
    // 更新新增用户图表
    StatisticsState.charts.userNewChart.data.labels = dateRange;
    StatisticsState.charts.userNewChart.data.datasets[0].data = dateRange.map(date => newUsersData[date] || 0);
    StatisticsState.charts.userNewChart.data.datasets[1].data = dateRange.map(date => newNormalUsersData[date] || 0);
    StatisticsState.charts.userNewChart.data.datasets[2].data = dateRange.map(date => newVipUsersData[date] || 0);
    StatisticsState.charts.userNewChart.data.datasets[3].data = dateRange.map(date => zeroReadingUsersData[date] || 0);
    StatisticsState.charts.userNewChart.data.datasets[4].data = dateRange.map(date => experienceToRegisterUsersData[date] || 0);
    StatisticsState.charts.userNewChart.update();
    
    // 更新国家分布图表
    if (countryStats && countryStats.length) {
      // 限制显示的国家数量，只显示前15个国家
      const topCountries = countryStats.slice(0, 15);
      
      // 准备图表数据
      const countryLabels = topCountries.map(item => {
        // 将国家二字码转换为国家名称，如果有需要可以添加映射
        const countryMap = {
          'CN': '中国',
          'US': '美国',
          'JP': '日本',
          'KR': '韩国',
          'GB': '英国',
          'CA': '加拿大',
          'AU': '澳大利亚',
          'DE': '德国',
          'FR': '法国',
          'IT': '意大利',
          'ES': '西班牙',
          'RU': '俄罗斯',
          'BR': '巴西',
          'IN': '印度',
          'SG': '新加坡'
          // 可以根据需要添加更多国家映射
        };
        
        return countryMap[item.country] || item.country;
      });
      
      const totalUsers = topCountries.map(item => item.user_count);
      
      // 更新图表数据
      StatisticsState.charts.userCountryChart.data.labels = countryLabels;
      StatisticsState.charts.userCountryChart.data.datasets[0].data = totalUsers;
      StatisticsState.charts.userCountryChart.update();
    } else {
      console.warn('国家分布数据不完整');
    }
  } catch (error) {
    console.error('更新用户图表出错:', error);
    showError('更新用户图表失败: ' + error.message);
  }
}

// 更新用户统计摘要
function updateUserSummary() {
  if (!StatisticsState.data.userData) return;
  
  try {
    const { dateRange, totalUsersData, normalUsersData, vipUsersData, newUsersData } = StatisticsState.data.userData;
    
    // 检查数据完整性
    if (!dateRange || !dateRange.length || !totalUsersData || !normalUsersData || !vipUsersData || !newUsersData) {
      console.warn('用户统计数据不完整');
      
      // 重置统计显示
      $('#total-users').text('0');
      $('#total-normal-users').text('0');
      $('#total-vip-users').text('0');
      $('#new-users').text('0');
      return;
    }
    
    // 获取最后一天的数据
    const lastDate = dateRange[dateRange.length - 1];
    
    // 确保最后一天的数据存在
    if (totalUsersData[lastDate] === undefined || 
        normalUsersData[lastDate] === undefined || 
        vipUsersData[lastDate] === undefined) {
      console.warn('最后一天的用户数据不完整');
      return;
    }
    
    // 设置总用户数
    $('#total-users').text(totalUsersData[lastDate].toLocaleString());
    
    // 设置普通用户数
    $('#total-normal-users').text(normalUsersData[lastDate].toLocaleString());
    
    // 设置VIP用户数
    $('#total-vip-users').text(vipUsersData[lastDate].toLocaleString());
    
    // 计算新增用户总数
    const totalNewUsers = dateRange.reduce((sum, date) => sum + (newUsersData[date] || 0), 0);
    $('#new-users').text(totalNewUsers.toLocaleString());
  } catch (error) {
    console.error('更新用户统计摘要出错:', error);
    showError('更新用户统计摘要失败: ' + error.message);
  }
} 