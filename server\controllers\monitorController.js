const { pool } = require('../config/db');
const moment = require('moment');

// 检查匿名占卜记录中的重复IP或浏览器指纹
// 检查条件：anonymous_divination_records表中created_at >= 2024-07-29的数据中，是否存在重复IP或浏览器指纹
async function checkAnonymousDuplicates(req, res) {
  try {
    const targetDate = '2025-07-29';
    
    // 检查重复IP
    const [duplicateIPs] = await pool.query(`
      SELECT 
        ip_address,
        COUNT(*) as count,
        GROUP_CONCAT(DISTINCT id ORDER BY created_at) as record_ids,
        MIN(created_at) as first_occurrence,
        MAX(created_at) as last_occurrence
      FROM anonymous_divination_records 
      WHERE created_at >= ? AND ip_address IS NOT NULL AND ip_address != ''
      GROUP BY ip_address 
      HAVING COUNT(*) > 1
      ORDER BY count DESC, first_occurrence DESC
    `, [targetDate + ' 00:00:00']);

    // 检查重复浏览器指纹
    const [duplicateFingerprints] = await pool.query(`
      SELECT 
        LEFT(browser_fingerprint, 100) as fingerprint_preview,
        COUNT(*) as count,
        GROUP_CONCAT(DISTINCT id ORDER BY created_at) as record_ids,
        MIN(created_at) as first_occurrence,
        MAX(created_at) as last_occurrence
      FROM anonymous_divination_records 
      WHERE created_at >= ? AND browser_fingerprint IS NOT NULL AND browser_fingerprint != ''
      GROUP BY browser_fingerprint 
      HAVING COUNT(*) > 1
      ORDER BY count DESC, first_occurrence DESC
    `, [targetDate + ' 00:00:00']);

    res.json({
      success: true,
      data: {
        duplicateIPs,
        duplicateFingerprints,
        summary: {
          duplicateIPCount: duplicateIPs.length,
          duplicateFingerprintCount: duplicateFingerprints.length,
          totalDuplicateIPRecords: duplicateIPs.reduce((sum, item) => sum + item.count, 0),
          totalDuplicateFingerprintRecords: duplicateFingerprints.reduce((sum, item) => sum + item.count, 0)
        }
      }
    });
  } catch (error) {
    console.error('检查匿名占卜重复数据失败:', error);
    res.status(500).json({
      success: false,
      message: '检查匿名占卜重复数据失败',
      error: error.message
    });
  }
}

// 检查匿名占卜记录中spread_id为空的情况
// 检查条件：anonymous_divination_records表中created_at >= 2024-07-29的数据中，是否存在spread_id为空的情况
async function checkAnonymousEmptySpreadId(req, res) {
  try {
    const targetDate = '2025-07-29';
    
    const [emptySpreadIdRecords] = await pool.query(`
      SELECT 
        id,
        session_id,
        ip_address,
        LEFT(browser_fingerprint, 100) as fingerprint_preview,
        LEFT(question, 100) as question_preview,
        created_at
      FROM anonymous_divination_records 
      WHERE created_at >= ? AND (spread_id IS NULL OR spread_id = '')
      ORDER BY created_at DESC
    `, [targetDate + ' 00:00:00']);

    // 统计总数
    const [[totalCount]] = await pool.query(`
      SELECT COUNT(*) as total
      FROM anonymous_divination_records 
      WHERE created_at >= ? AND (spread_id IS NULL OR spread_id = '')
    `, [targetDate + ' 00:00:00']);

    res.json({
      success: true,
      data: {
        emptySpreadIdRecords,
        summary: {
          totalCount: totalCount.total,
          recordsShown: emptySpreadIdRecords.length
        }
      }
    });
  } catch (error) {
    console.error('检查匿名占卜空spread_id失败:', error);
    res.status(500).json({
      success: false,
      message: '检查匿名占卜空spread_id失败',
      error: error.message
    });
  }
}

// 检查sessions表中非VIP用户的多次completed状态
// 检查条件：sessions表中timestamp >= 2024-07-29的数据中，
// 对于非VIP用户，检查其实际占卜次数是否超过了剩余占卜次数（remaining_reads）
// 这样可以发现可能的滥用行为（用户占卜次数超过了其应有的次数）
async function checkNonVipMultipleCompleted(req, res) {
  try {
    const targetDate = '2025-07-29';

    const [nonVipMultipleCompleted] = await pool.query(`
      SELECT
        s.user_id,
        u.username,
        u.email,
        u.vip_status,
        u.remaining_reads,
        COUNT(*) as completed_count,
        (COUNT(*) - u.remaining_reads) as excess_count,
        GROUP_CONCAT(DISTINCT s.id ORDER BY s.timestamp) as session_ids,
        MIN(s.timestamp) as first_session,
        MAX(s.timestamp) as last_session
      FROM sessions s
      JOIN users u ON s.user_id = u.id
      WHERE s.timestamp >= ?
        AND s.status = 'completed'
        AND (s.spread_id != 'daily_fortune' OR s.spread_id IS NULL OR s.spread_id = '')
        AND u.vip_status != 'active'
      GROUP BY s.user_id, u.username, u.email, u.vip_status, u.remaining_reads
      HAVING COUNT(*) > u.remaining_reads
      ORDER BY (COUNT(*) - u.remaining_reads) DESC, completed_count DESC, last_session DESC
    `, [targetDate + ' 00:00:00']);

    res.json({
      success: true,
      data: {
        nonVipMultipleCompleted,
        summary: {
          affectedUsersCount: nonVipMultipleCompleted.length,
          totalSuspiciousSessions: nonVipMultipleCompleted.reduce((sum, item) => sum + item.completed_count, 0)
        }
      }
    });
  } catch (error) {
    console.error('检查非VIP用户多次completed失败:', error);
    res.status(500).json({
      success: false,
      message: '检查非VIP用户多次completed失败',
      error: error.message
    });
  }
}

// 检查sessions表中status=completed且spread_id为空的情况
// 检查条件：sessions表中timestamp >= 2024-07-29的数据中，status=completed且spread_id为空的情况
async function checkSessionsEmptySpreadId(req, res) {
  try {
    const targetDate = '2025-07-29';

    const [emptySpreadIdSessions] = await pool.query(`
      SELECT
        s.id,
        s.user_id,
        u.username,
        u.email,
        u.vip_status,
        LEFT(s.question, 100) as question_preview,
        s.timestamp,
        s.spread_id
      FROM sessions s
      JOIN users u ON s.user_id = u.id
      WHERE s.timestamp >= ?
        AND s.status = 'completed'
        AND (s.spread_id IS NULL OR s.spread_id = '')
      ORDER BY s.timestamp DESC
    `, [targetDate + ' 00:00:00']);

    // 统计总数
    const [[totalCount]] = await pool.query(`
      SELECT COUNT(*) as total
      FROM sessions s
      WHERE s.timestamp >= ?
        AND s.status = 'completed'
        AND (s.spread_id IS NULL OR s.spread_id = '')
    `, [targetDate + ' 00:00:00']);

    res.json({
      success: true,
      data: {
        emptySpreadIdSessions,
        summary: {
          totalCount: totalCount.total,
          recordsShown: emptySpreadIdSessions.length
        }
      }
    });
  } catch (error) {
    console.error('检查sessions空spread_id失败:', error);
    res.status(500).json({
      success: false,
      message: '检查sessions空spread_id失败',
      error: error.message
    });
  }
}

// 获取监控概览数据
async function getMonitorOverview(req, res) {
  try {
    const targetDate = '2025-07-29';
    
    // 并行执行所有检查
    const [
      [duplicateIPCount],
      [duplicateFingerprintCount],
      [anonymousEmptySpreadCount],
      [nonVipMultipleCount],
      [sessionsEmptySpreadCount]
    ] = await Promise.all([
      // 重复IP数量
      pool.query(`
        SELECT COUNT(DISTINCT ip_address) as count
        FROM (
          SELECT ip_address, COUNT(*) as cnt
          FROM anonymous_divination_records 
          WHERE created_at >= ? AND ip_address IS NOT NULL AND ip_address != ''
          GROUP BY ip_address 
          HAVING COUNT(*) > 1
        ) as duplicates
      `, [targetDate + ' 00:00:00']),
      
      // 重复指纹数量
      pool.query(`
        SELECT COUNT(DISTINCT browser_fingerprint) as count
        FROM (
          SELECT browser_fingerprint, COUNT(*) as cnt
          FROM anonymous_divination_records 
          WHERE created_at >= ? AND browser_fingerprint IS NOT NULL AND browser_fingerprint != ''
          GROUP BY browser_fingerprint 
          HAVING COUNT(*) > 1
        ) as duplicates
      `, [targetDate + ' 00:00:00']),
      
      // 匿名记录空spread_id数量
      pool.query(`
        SELECT COUNT(*) as count
        FROM anonymous_divination_records 
        WHERE created_at >= ? AND (spread_id IS NULL OR spread_id = '')
      `, [targetDate + ' 00:00:00']),
      
      // 非VIP用户占卜次数超过剩余次数的用户数量
      pool.query(`
        SELECT COUNT(*) as count
        FROM (
          SELECT s.user_id
          FROM sessions s
          JOIN users u ON s.user_id = u.id
          WHERE s.timestamp >= ?
            AND s.status = 'completed'
            AND (s.spread_id != 'daily_fortune' OR s.spread_id IS NULL OR s.spread_id = '')
            AND u.vip_status != 'active'
          GROUP BY s.user_id, u.remaining_reads
          HAVING COUNT(*) > u.remaining_reads
        ) as subquery
      `, [targetDate + ' 00:00:00']),
      
      // sessions空spread_id数量
      pool.query(`
        SELECT COUNT(*) as count
        FROM sessions s
        WHERE s.timestamp >= ? 
          AND s.status = 'completed' 
          AND (s.spread_id IS NULL OR s.spread_id = '')
      `, [targetDate + ' 00:00:00'])
    ]);

    res.json({
      success: true,
      data: {
        duplicateIPs: duplicateIPCount[0]?.count || 0,
        duplicateFingerprints: duplicateFingerprintCount[0]?.count || 0,
        anonymousEmptySpreadId: anonymousEmptySpreadCount[0]?.count || 0,
        nonVipMultipleCompleted: nonVipMultipleCount[0]?.count || 0,
        sessionsEmptySpreadId: sessionsEmptySpreadCount[0]?.count || 0,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('获取监控概览失败:', error);
    res.status(500).json({
      success: false,
      message: '获取监控概览失败',
      error: error.message
    });
  }
}

module.exports = {
  checkAnonymousDuplicates,
  checkAnonymousEmptySpreadId,
  checkNonVipMultipleCompleted,
  checkSessionsEmptySpreadId,
  getMonitorOverview
};
