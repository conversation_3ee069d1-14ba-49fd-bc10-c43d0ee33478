const express = require('express');
const router = express.Router();
const shareStatsController = require('../controllers/shareStatsController');
const userStatsController = require('../controllers/userStatsController');
const sessionStatsController = require('../controllers/sessionStatsController');
const costStatsController = require('../controllers/costStatsController');
const incomeStatsController = require('../controllers/incomeStatsController');
const monitorController = require('../controllers/monitorController');

// 获取分享数统计数据
router.get('/share-stats', shareStatsController.getShareStats);

// 获取用户统计数据
router.get('/user-stats', userStatsController.getUserStats);

// 获取sessions统计数据
router.get('/session-stats', sessionStatsController.getSessionStats);

// 获取sessions明细数据
router.get('/session-details', sessionStatsController.getSessionDetails);

// 获取单个会话详情
router.get('/session-detail/:id', sessionStatsController.getSessionDetail);

// 获取成本统计数据
router.get('/cost-stats', costStatsController.getCostStats);

// 获取收入统计数据
router.get('/income-stats', incomeStatsController.getIncomeStats);

// 获取监控概览数据
router.get('/monitor-overview', monitorController.getMonitorOverview);

// 检查匿名占卜记录重复数据
router.get('/monitor/anonymous-duplicates', monitorController.checkAnonymousDuplicates);

// 检查匿名占卜记录空spread_id
router.get('/monitor/anonymous-empty-spread', monitorController.checkAnonymousEmptySpreadId);

// 检查非VIP用户多次completed
router.get('/monitor/non-vip-multiple', monitorController.checkNonVipMultipleCompleted);

// 检查sessions空spread_id
router.get('/monitor/sessions-empty-spread', monitorController.checkSessionsEmptySpreadId);

module.exports = router;