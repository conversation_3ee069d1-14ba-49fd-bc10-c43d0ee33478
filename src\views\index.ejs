<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数据统计分析</title>
  <!-- 引入外部库 -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
  <script src="https://cdn.jsdelivr.net/npm/jquery/dist/jquery.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/moment/moment.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <!-- Font Awesome图标库 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- 自定义样式和脚本 -->
  <link rel="stylesheet" href="/css/statistics.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>数据统计分析</h1>
      <div class="date-filter">
        <input type="text" id="daterange" class="date-input">
        <button id="refresh" class="btn-refresh">刷新数据</button>
      </div>
    </header>

    <div id="error-container" class="error-message"></div>

    <!-- 数据类型选择器 -->
    <div class="data-type-selector">
      <div class="data-type-tabs">
        <div class="data-type-tab active" data-type="share">分享数统计</div>
        <div class="data-type-tab" data-type="user">用户统计</div>
        <div class="data-type-tab" data-type="session">会话统计</div>
        <div class="data-type-tab" data-type="cost">成本统计</div>
        <div class="data-type-tab" data-type="income">收入统计</div>
        <div class="data-type-tab" data-type="diary">日记</div>
      </div>
    </div>

    <!-- 分享数统计部分 -->
    <div id="share-stats-container">
      <div class="platform-selector">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <label for="platform-select" style="margin-right: 10px;">选择平台:</label>
        </div>
        <select id="platform-select">
          <option value="all">全部平台</option>
        </select>
      </div>

      <div class="stats-summary">
        <div class="stat-card share-total">
          <h3>总分享数</h3>
          <div class="stat-value" id="total-shares">0</div>
          <div class="stat-desc">所选时间范围内的总分享数量</div>
        </div>
        <div class="stat-card share-pending">
          <h3>待审核分享</h3>
          <div class="stat-value" id="pending-shares">0</div>
          <div class="stat-desc">所选时间范围内的待审核分享数量</div>
        </div>
        <div class="stat-card share-approved">
          <h3>已通过分享</h3>
          <div class="stat-value" id="approved-shares">0</div>
          <div class="stat-desc">所选时间范围内的已通过分享数量</div>
        </div>
        <div class="stat-card share-rewarded">
          <h3>已奖励分享</h3>
          <div class="stat-value" id="rewarded-shares">0</div>
          <div class="stat-desc">所选时间范围内的已奖励分享数量</div>
        </div>
      </div>

      <div class="chart-container">
        <div class="chart-tabs">
          <div class="chart-tab active" data-type="share-trend">分享趋势图</div>
          <div class="chart-tab" data-type="share-platform">平台分布图</div>
          <div class="chart-tab" data-type="share-status">状态分布图</div>
        </div>

        <div class="chart-wrapper" id="chart-share-trend">
          <canvas id="shareTrendChart"></canvas>
        </div>

        <div class="chart-wrapper" id="chart-share-platform" style="display:none;">
          <canvas id="sharePlatformChart"></canvas>
        </div>

        <div class="chart-wrapper" id="chart-share-status" style="display:none;">
          <canvas id="shareStatusChart"></canvas>
        </div>
      </div>
    </div>

    <!-- 用户统计部分 -->
    <div id="user-stats-container" style="display:none;">
      <div class="stats-summary">
        <div class="stat-card user-total">
          <h3>总用户数</h3>
          <div class="stat-value" id="total-users">0</div>
          <div class="stat-desc">截至所选日期的总用户数量</div>
        </div>
        <div class="stat-card user-normal">
          <h3>普通用户数</h3>
          <div class="stat-value" id="total-normal-users">0</div>
          <div class="stat-desc">截至所选日期的普通用户数量</div>
        </div>
        <div class="stat-card user-vip">
          <h3>VIP用户数</h3>
          <div class="stat-value" id="total-vip-users">0</div>
          <div class="stat-desc">截至所选日期的VIP用户数量</div>
        </div>
        <div class="stat-card user-new">
          <h3>新增用户数</h3>
          <div class="stat-value" id="new-users">0</div>
          <div class="stat-desc">所选时间范围内的新增用户数量</div>
        </div>
      </div>

      <div class="chart-container">
        <div class="chart-tabs">
          <div class="chart-tab active" data-type="user-new">新增用户趋势</div>
          <div class="chart-tab" data-type="user-country">用户国家分布</div>
        </div>

        <div class="chart-wrapper" id="chart-user-new">
          <div class="loading">加载中...</div>
        </div>

        <div class="chart-wrapper" id="chart-user-country" style="display:none;">
          <div class="loading">加载中...</div>
        </div>
      </div>
    </div>

    <!-- 会话统计部分 -->
    <div id="session-stats-container" style="display:none;">
      <div class="stats-summary">
        <div class="stat-card session-total">
          <h3>总会话数</h3>
          <div class="stat-value" id="total-sessions">0</div>
          <div class="stat-desc">截至所选日期的总会话数量</div>
        </div>
        <div class="stat-card session-new">
          <h3>总新增会话数</h3>
          <div class="stat-value" id="new-sessions">0</div>
          <div class="stat-desc">所选时间范围内的总新增会话数量</div>
        </div>
        <div class="stat-card session-anonymous">
          <h3>未登录会话数</h3>
          <div class="stat-value" id="anonymous-sessions">0</div>
          <div class="stat-desc">所选时间范围内的未登录会话数量</div>
        </div>
        <div class="stat-card session-daily-fortune">
          <h3>日运占卜会话</h3>
          <div class="stat-value" id="daily-fortune-sessions">0</div>
          <div class="stat-desc">所选时间范围内的日运占卜会话数量</div>
        </div>
        <div class="stat-card session-divination">
          <h3>是非占卜会话</h3>
          <div class="stat-value" id="divination-sessions">0</div>
          <div class="stat-desc">所选时间范围内的是非占卜会话数量</div>
        </div>
        <div class="stat-card session-newline">
          <h3>换行符类型</h3>
          <div class="stat-value">
            <span id="single-newline">0</span> / <span id="double-newline">0</span>
          </div>
          <div class="stat-desc">单行/双行换行符会话数量</div>
        </div>
        <div class="stat-card session-paragraph">
          <h3>分段检测状态</h3>
          <div class="stat-value">
            <span id="paragraph-success">0</span> / <span id="paragraph-fail">0</span>
          </div>
          <div class="stat-desc">成功/失败分段检测会话数量</div>
        </div>
      </div>

      <div class="chart-container">
        <div class="chart-tabs">
          <div class="chart-tab active" data-type="session-new">新增会话趋势</div>
        </div>

        <div class="chart-wrapper" id="chart-session-new">
          <div class="loading">加载中...</div>
          <canvas id="sessionNewChart"></canvas>
        </div>
      </div>
      
      <!-- 会话明细部分 -->
      <div class="session-details-container">
        <h3>会话明细查询</h3>
        <div class="session-details-controls">
          <div class="search-box">
            <input type="text" id="session-search" placeholder="搜索问题内容...">
            <input type="text" id="session-id-search" placeholder="会话ID...">
            <button id="session-search-btn">搜索</button>
          </div>
          <div class="filter-options">
            <select id="status-filter" class="status-select">
              <option value="">所有状态</option>
              <option value="pending">待处理</option>
              <option value="completed">已完成</option>
              <option value="ethical_intervention">安全检测会话</option>
              <option value="potential_ethical_issue">潜在安全检测会话</option>
            </select>
          </div>
        </div>
        <div class="session-details-table-container">
          <table class="session-details-table">
            <thead>
              <tr>
                <th>时间</th>
                <th>会话ID</th>
                <th>问题内容</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody id="session-details-body">
              <!-- 会话数据将通过JS动态填充 -->
            </tbody>
          </table>
        </div>
        <div class="session-pagination">
          <button id="prev-page" class="pagination-btn">上一页</button>
          <span id="page-info">第 1 页 / 共 1 页</span>
          <button id="next-page" class="pagination-btn">下一页</button>
        </div>
      </div>
    </div>

    <!-- 成本统计部分 -->
    <div id="cost-stats-container" style="display:none;">
      <div class="stats-summary">
        <div class="stat-card cost-total">
          <h3>总成本</h3>
          <div class="stat-value" id="total-cost">¥0.00</div>
        </div>
        <div class="stat-card cost-model">
          <h3>登录用户模型调用成本</h3>
          <div class="stat-value" id="model-cost">¥0.00</div>
          <div class="stat-price">输入：¥0.0024/千Token<br>输出：¥0.0096/千Token</div>
        </div>
        <div class="stat-card cost-anonymous">
          <h3>未登录用户模型调用成本</h3>
          <div class="stat-value" id="anonymous-cost">¥0.00</div>
          <div class="stat-price">输入：¥0.0008/千Token<br>输出：¥0.002/千Token</div>
        </div>
        <div class="stat-card cost-daily-fortune">
          <h3>日运占卜成本</h3>
          <div class="stat-value" id="daily-fortune-cost">¥0.00</div>
          <div class="stat-price">输入：¥0.0003/千Token<br>输出：¥0.0006/千Token</div>
        </div>
        <!-- <div class="stat-card cost-ethical">
          <h3>伦理检查成本</h3>
          <div class="stat-value" id="ethical-cost">¥0.00</div>
          <div class="stat-price">输入：¥0.0003/千Token<br>输出：¥0.0006/千Token</div>
        </div> -->
        <div class="stat-card cost-spread-recom">
          <h3>AI推荐牌阵成本</h3>
          <div class="stat-value" id="spread-recom-cost">¥0.00</div>
          <div class="stat-price">输入：¥0.0003/千Token<br>输出：¥0.0006/千Token</div>
        </div>
        <div class="stat-card cost-horoscope">
          <h3>星座运势成本</h3>
          <div class="stat-value" id="horoscope-cost">¥0.00</div>
          <div class="stat-price">输入：¥0.0008/千Token<br>输出：¥0.002/千Token</div>
        </div>
        <div class="stat-card cost-tts">
          <h3>TTS语音成本</h3>
          <div class="stat-value" id="tts-cost">¥0.00</div>
          <div class="stat-price">¥0.0005/千字符</div>
        </div>
        <div class="stat-card cost-avg">
          <h3>平均会话成本</h3>
          <div class="stat-value" id="avg-cost">¥0.00</div>
        </div>
        <div class="stat-card cost-spread-recom-time">
          <h3>AI推荐牌阵处理时间</h3>
          <div class="stat-value" id="avg-spread-recom-time">0.00秒</div>
        </div>
      </div>

      <div class="chart-container">
        <div class="chart-tabs">
          <div class="chart-tab active" data-type="cost-daily">每日成本趋势</div>
          <div class="chart-tab" data-type="spread-recom-time">AI推荐牌阵处理时间</div>
        </div>
        
        <div class="chart-wrapper" id="chart-cost-daily">
          <div class="loading">加载中...</div>
          <canvas id="costDailyChart"></canvas>
        </div>
        
        <div class="chart-wrapper" id="chart-spread-recom-time" style="display:none;">
          <div class="loading">加载中...</div>
          <canvas id="spreadRecomTimeChart"></canvas>
        </div>
      </div>
    </div>

    <!-- 收入统计部分 -->
    <div id="income-stats-container" style="display:none;">
      <div class="stats-summary">
        <div class="stat-card income-total">
          <h3>总收入</h3>
          <div class="stat-value" id="total-income">¥0.00</div>
          <div class="stat-desc">所选时间范围内的总收入</div>
        </div>
        <div class="stat-card income-wechat">
          <h3>微信支付</h3>
          <div class="stat-value" id="wechat-income">¥0.00</div>
          <div class="stat-desc">微信支付收入</div>
        </div>
        <div class="stat-card income-alipay">
          <h3>支付宝</h3>
          <div class="stat-value" id="alipay-income">¥0.00</div>
          <div class="stat-desc">支付宝收入</div>
        </div>
        <div class="stat-card income-paypal">
          <h3>PayPal</h3>
          <div class="stat-value" id="paypal-income">¥0.00</div>
          <div class="stat-desc">PayPal收入</div>
        </div>
        <div class="stat-card income-orders">
          <h3>订单数量</h3>
          <div class="stat-value" id="order-count">0</div>
          <div class="stat-desc">成功支付的订单数量</div>
        </div>
      </div>

      <div class="chart-container">
        <div class="chart-tabs">
          <div class="chart-tab active" data-type="income-daily">每日收入趋势</div>
          <div class="chart-tab" data-type="income-product">产品收入分布</div>
        </div>
        
        <div class="chart-wrapper" id="chart-income-daily">
          <div class="loading">加载中...</div>
        </div>
        
        <div class="chart-wrapper" id="chart-income-product" style="display:none;">
          <div class="loading">加载中...</div>
        </div>
      </div>
    </div>
    
    <!-- 日记部分 -->
    <div id="diary-container" style="display:none;">
      <div class="diary-controls">
        <button id="add-diary" class="btn-add-diary">添加新日记</button>
      </div>
      
      <div class="diary-list">
        <!-- 日记列表将通过JS动态填充 -->
      </div>
      
      <!-- 添加/编辑日记对话框 -->
      <div id="diary-modal" class="modal">
        <div class="modal-content">
          <div class="modal-header">
            <h2 id="diary-modal-title">添加新日记</h2>
            <span class="close-modal">&times;</span>
          </div>
          <div class="modal-body">
            <div class="diary-form">
              <div class="form-group">
                <label for="diary-date">日期</label>
                <input type="date" id="diary-date" class="form-control" required>
              </div>
              <div class="form-group">
                <label for="diary-title">标题</label>
                <input type="text" id="diary-title" class="form-control" placeholder="请输入标题" required>
              </div>
              <div class="form-group">
                <label for="diary-content">内容</label>
                <textarea id="diary-content" class="form-control" rows="10" placeholder="请输入日记内容" required></textarea>
              </div>
              <div class="form-actions">
                <button id="save-diary" class="btn-save">保存</button>
                <button id="cancel-diary" class="btn-cancel">取消</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 会话详情模态框 -->
  <div id="session-detail-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="detail-modal-title">会话详情</h2>
        <span class="close-modal">&times;</span>
      </div>
      <div class="modal-body">
        <div class="session-detail-info">
          <div class="detail-item">
            <span class="detail-label">问题内容：</span>
            <div id="detail-question" class="detail-value"></div>
          </div>
          <div class="detail-item">
            <span class="detail-label">时间：</span>
            <div id="detail-timestamp" class="detail-value"></div>
          </div>
          <div class="detail-item">
            <span class="detail-label">状态：</span>
            <div id="detail-status" class="detail-value"></div>
          </div>
        </div>
        
        <!-- 安全检测信息区域 -->
        <div id="security-info-container" style="display:none;" class="security-info">
          <div class="security-info-item">
            <span class="security-label">检测类型：</span>
            <div id="ethical-category" class="security-value"></div>
          </div>
          <div class="security-info-item">
            <span class="security-label">检测原因：</span>
            <div id="ethical-reason" class="security-value"></div>
          </div>
          <div class="security-info-item">
            <span class="security-label">置信度：</span>
            <div id="ethical-confidence" class="security-value"></div>
          </div>
        </div>
        
        <div class="detail-tabs">
          <div class="detail-tab active" data-tab="reading-result">塔罗解读</div>
          <div class="detail-tab" data-tab="deep-analysis">深度分析</div>
          <div class="detail-tab" data-tab="dialog-history">追问历史</div>
        </div>
        
        <div class="detail-content">
          <div id="reading-result-content" class="tab-content active">
            <div class="loading">加载中...</div>
          </div>
          <div id="deep-analysis-content" class="tab-content">
            <div class="loading">加载中...</div>
          </div>
          <div id="dialog-history-content" class="tab-content">
            <div class="loading">加载中...</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入拆分后的JS文件 -->
  <script src="/js/statistics/share-stats.js"></script>
  <script src="/js/statistics/user-stats.js"></script>
  <script src="/js/statistics/session-stats.js"></script>
  <script src="/js/statistics/cost-stats.js"></script>
  <script src="/js/statistics/income-stats.js"></script>
  <script src="/js/statistics/diary.js"></script>
  <script src="/js/statistics/main.js"></script>
</body>
</html> 