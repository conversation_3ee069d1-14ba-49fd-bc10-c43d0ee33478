const axios = require('axios');

async function testUpdatedMonitor() {
  const baseURL = 'http://localhost:5001';
  
  try {
    console.log('测试更新后的监控概览API...');
    const overviewResponse = await axios.get(`${baseURL}/api/monitor-overview`);
    console.log('监控概览结果:', JSON.stringify(overviewResponse.data, null, 2));
    
    console.log('\n测试更新后的非VIP滥用检查API...');
    const nonVipResponse = await axios.get(`${baseURL}/api/monitor/non-vip-multiple`);
    console.log('非VIP滥用检查结果:', JSON.stringify(nonVipResponse.data, null, 2));
    
  } catch (error) {
    console.error('API测试失败:', error.message);
    if (error.response) {
      console.error('响应数据:', error.response.data);
    }
  }
}

testUpdatedMonitor();
